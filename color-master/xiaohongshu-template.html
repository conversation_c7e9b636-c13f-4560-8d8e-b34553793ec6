<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书软件工具分享模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            padding: 20px;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 15px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8em;
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .attract { background: linear-gradient(45deg, #ff6b6b, #ff8e8e); }
        .info { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .features { background: linear-gradient(45deg, #feca57, #ff9ff3); }
        .tutorial { background: linear-gradient(45deg, #1dd1a1, #55efc4); }
        .experience { background: linear-gradient(45deg, #fd79a8, #fdcb6e); }
        .audience { background: linear-gradient(45deg, #6c5ce7, #a29bfe); }
        .download { background: linear-gradient(45deg, #00b894, #00cec9); }
        .interaction { background: linear-gradient(45deg, #e17055, #fab1a0); }
        
        .content {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
        
        .feature-item, .step-item, .point-item {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            border-left: 4px solid #ff6b6b;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover, .step-item:hover, .point-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-number, .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .checkmark {
            color: #00b894;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .crossmark {
            color: #e17055;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: transform 0.3s ease;
        }
        
        .tag:hover {
            transform: scale(1.1);
        }
        
        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            margin: 8px 0;
            transition: border-color 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }
        
        .placeholder {
            color: #999;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 小红书软件工具分享模板</h1>
            <p>让每个软件分享都成为爆款笔记！</p>
        </div>

        <!-- 开头吸引部分 -->
        <div class="section">
            <div class="section-title attract">🎯 开头吸引</div>
            <div class="content">
                <p><span class="emoji">🔥</span>又发现一个宝藏软件！<input type="text" class="input-field" placeholder="[具体痛点，如：总是忘记重要事情]">的姐妹们有救了！</p>
                <p><span class="emoji">✨</span>这个<input type="text" class="input-field" placeholder="[软件名称]">真的太好用了，<input type="text" class="input-field" placeholder="[核心价值，如：让我的工作效率提升了200%]">！</p>
            </div>
        </div>

        <!-- 软件基本信息 -->
        <div class="section">
            <div class="section-title info">📱 软件基本信息</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>软件名称：</strong><input type="text" class="input-field" placeholder="[软件名称]">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>适用系统：</strong><input type="text" class="input-field" placeholder="iOS/Android/Windows/Mac">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>收费情况：</strong><input type="text" class="input-field" placeholder="免费/付费/免费试用">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>软件大小：</strong><input type="text" class="input-field" placeholder="[具体大小，如：50MB]">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>推荐指数：</strong><span class="highlight">⭐⭐⭐⭐⭐</span>
                </div>
            </div>
        </div>

        <!-- 核心功能介绍 -->
        <div class="section">
            <div class="section-title features">💡 核心功能介绍</div>
            <div class="content">
                <div class="feature-item">
                    <span class="feature-number">1</span>
                    <strong><input type="text" class="input-field" placeholder="[功能一名称]"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[具体作用]"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[使用感受]"></div>
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-number">2</span>
                    <strong><input type="text" class="input-field" placeholder="[功能二名称]"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[具体作用]"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[使用感受]"></div>
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-number">3</span>
                    <strong><input type="text" class="input-field" placeholder="[功能三名称]"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[具体作用]"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="[使用感受]"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用教程/技巧 -->
        <div class="section">
            <div class="section-title tutorial">🎨 使用教程/技巧</div>
            <div class="content">
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第一步：</strong><input type="text" class="input-field" placeholder="[具体操作步骤]">
                </div>
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第二步：</strong><input type="text" class="input-field" placeholder="[具体操作步骤]">
                </div>
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第三步：</strong><input type="text" class="input-field" placeholder="[具体操作步骤]">
                </div>
                <div class="step-item" style="background: rgba(255, 206, 84, 0.2); border-left-color: #feca57;">
                    <span class="emoji">💡</span><strong>小贴士：</strong><input type="text" class="input-field" placeholder="[重要提醒或高级技巧]">
                </div>
            </div>
        </div>

        <!-- 实际体验分享 -->
        <div class="section">
            <div class="section-title experience">🔥 实际体验分享</div>
            <div class="content">
                <p><span class="emoji">⏰</span>用了<input type="text" class="input-field" placeholder="[使用时间，如：一个月]">，真的爱了！</p>

                <div class="point-item" style="border-left-color: #00b894;">
                    <span class="checkmark">✅</span><strong>优点：</strong>
                    <div style="margin-left: 20px; margin-top: 10px;">
                        <div>• <input type="text" class="input-field" placeholder="[优点1]"></div>
                        <div>• <input type="text" class="input-field" placeholder="[优点2]"></div>
                        <div>• <input type="text" class="input-field" placeholder="[优点3]"></div>
                    </div>
                </div>

                <div class="point-item" style="border-left-color: #e17055;">
                    <span class="crossmark">❌</span><strong>缺点：</strong>
                    <div style="margin-left: 20px; margin-top: 10px;">
                        <div>• <input type="text" class="input-field" placeholder="[缺点1]"></div>
                        <div>• <input type="text" class="input-field" placeholder="[缺点2]"></div>
                    </div>
                </div>

                <div class="point-item" style="border-left-color: #fdcb6e;">
                    <span class="emoji">💰</span><strong>性价比：</strong><input type="text" class="input-field" placeholder="[性价比评价]">
                </div>
            </div>
        </div>

        <!-- 适合人群 -->
        <div class="section">
            <div class="section-title audience">👥 适合人群</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">✨</span><strong>学生党：</strong><input type="text" class="input-field" placeholder="[具体适用场景]">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong>上班族：</strong><input type="text" class="input-field" placeholder="[具体适用场景]">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong>自媒体人：</strong><input type="text" class="input-field" placeholder="[具体适用场景]">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong><input type="text" class="input-field" placeholder="[其他特定人群]">：</strong><input type="text" class="input-field" placeholder="[具体适用场景]">
                </div>
            </div>
        </div>

        <!-- 获取方式 -->
        <div class="section">
            <div class="section-title download">📥 获取方式</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>官方下载：</strong><input type="text" class="input-field" placeholder="[应用商店名称]">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>官网地址：</strong><input type="text" class="input-field" placeholder="[官网链接，如果有]">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>注意事项：</strong><input type="text" class="input-field" placeholder="[下载注意事项]">
                </div>
            </div>
        </div>

        <!-- 互动引导 -->
        <div class="section">
            <div class="section-title interaction">💬 互动引导</div>
            <div class="content">
                <p><span class="emoji">🤔</span>你们还在用什么好用的<input type="text" class="input-field" placeholder="[相关类型软件]">？</p>
                <p><span class="emoji">💭</span>评论区分享一下，一起变更高效！🤝</p>

                <div class="tags">
                    <span class="tag">#软件推荐</span>
                    <span class="tag">#效率工具</span>
                    <span class="tag">#<input type="text" style="background: transparent; border: none; color: white;" placeholder="软件类型"></span>
                    <span class="tag">#<input type="text" style="background: transparent; border: none; color: white;" placeholder="适用场景"></span>
                    <span class="tag">#干货分享</span>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="section" style="background: rgba(255, 255, 255, 0.1); border: 2px dashed #ff6b6b;">
            <div class="section-title" style="background: linear-gradient(45deg, #74b9ff, #0984e3); text-align: center;">
                📝 使用说明
            </div>
            <div class="content">
                <p><span class="emoji">💡</span><strong>如何使用这个模板：</strong></p>
                <ol style="margin-left: 20px; margin-top: 10px;">
                    <li>在每个输入框中填入对应的软件信息</li>
                    <li>根据软件特点调整内容的详细程度</li>
                    <li>复制填写完成的内容到小红书发布</li>
                    <li>记得配上精美的软件截图和使用效果图</li>
                </ol>
                <p style="margin-top: 15px;"><span class="emoji">🎯</span><strong>小贴士：</strong>保持内容真实性，分享真实使用感受更容易获得用户信任！</p>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.input-field');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 标签点击效果
            const tags = document.querySelectorAll('.tag');
            tags.forEach(tag => {
                tag.addEventListener('click', function() {
                    this.style.background = 'linear-gradient(45deg, #00b894, #00cec9)';
                    setTimeout(() => {
                        this.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                    }, 300);
                });
            });
        });
    </script>
</body>
</html>
